<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A408F713-43CC-4219-85C4-AE129FC2FA96}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RMS2018</RootNamespace>
    <AssemblyName>RMS2018</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <UseVSHostingProcess>true</UseVSHostingProcess>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>RMS2018.Program</StartupObject>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>bitbug_favicon.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Accessibility" />
    <Reference Include="BarBLL, Version=1.0.0.2, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\BarBLL.dll</HintPath>
    </Reference>
    <Reference Include="BarDAL, Version=1.0.0.2, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\BarDAL.dll</HintPath>
    </Reference>
    <Reference Include="BarModel, Version=1.0.0.2, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\BarModel.dll</HintPath>
    </Reference>
    <Reference Include="CefSharp, Version=1.25.7.0, Culture=neutral, PublicKeyToken=40c4b6fc221f4138, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\CefSharp.dll</HintPath>
    </Reference>
    <Reference Include="CefSharp.Wpf, Version=1.25.7.0, Culture=neutral, PublicKeyToken=40c4b6fc221f4138, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\CefSharp.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="ComponentApplicationServiceInterface, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\RMSModel\Packages\ComponentApplicationServiceInterface.dll</HintPath>
    </Reference>
    <Reference Include="ComponentWpfInterface, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\ComponentWpfInterface.dll</HintPath>
    </Reference>
    <Reference Include="ComponentWpfLibrary, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\ComponentWpf\ComponentWpf\bin\Debug\ComponentWpfLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Dapper, Version=1.39.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="Loya.Dameer, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Loya.Dameer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.mshtml">
      <HintPath>bin\Debug\Microsoft.mshtml.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="MoonPdfLib, Version=0.2.3.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\MoonPdfLib.dll</HintPath>
    </Reference>
    <Reference Include="MouseKeyboardActivityMonitor, Version=3.0.1.29653, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\MouseKeyboardActivityMonitor.dll</HintPath>
    </Reference>
    <Reference Include="MySql.Data, Version=6.5.7.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\MySql.Data.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=4.5.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationUI, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL" />
    <Reference Include="ReachFramework" />
    <Reference Include="SERVICE.PROXY">
      <HintPath>..\RMSModel\Packages\SERVICE.PROXY.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Printing" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="UIAutomationProvider" />
    <Reference Include="UIAutomationTypes" />
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="WPFToolkit, Version=3.5.40128.1, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\WPFToolkit.dll</HintPath>
    </Reference>
    <Reference Include="WPFVisifire.Charts, Version=4.5.2.1, Culture=neutral, PublicKeyToken=0cd785983078370b, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\WPFVisifire.Charts.dll</HintPath>
    </Reference>
    <Reference Include="WPFVisifire.Gauges, Version=4.5.2.1, Culture=neutral, PublicKeyToken=bf0af9b99ac47981, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\WPFVisifire.Gauges.dll</HintPath>
    </Reference>
    <Reference Include="Xceed.Wpf.Toolkit">
      <HintPath>..\packages\Xceed.Wpf.Toolkit.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="AccessBar.xaml.cs">
      <DependentUpon>AccessBar.xaml</DependentUpon>
    </Compile>
    <Compile Include="AppBook.xaml.cs">
      <DependentUpon>AppBook.xaml</DependentUpon>
    </Compile>
    <Compile Include="birthday_giftPay\birthdaygiftPayManageWindow.xaml.cs">
      <DependentUpon>birthdaygiftPayManageWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="ComponentWpf\lib\BuildFactory.cs" />
    <Compile Include="ComponentWpf\View\BookPage.xaml.cs">
      <DependentUpon>BookPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="ComponentWpf\View\HomePage.xaml.cs">
      <DependentUpon>HomePage.xaml</DependentUpon>
    </Compile>
    <Compile Include="ComponentWpf\View\LoginPage.xaml.cs">
      <DependentUpon>LoginPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="ComponentWpf\View\MainPage.xaml.cs">
      <DependentUpon>MainPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="ComponentWpf\View\OpenPage.xaml.cs">
      <DependentUpon>OpenPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="ComponentWpf\ViewModel\MainPageViewModel.cs" />
    <Compile Include="ComponentWpf\ViewModel\PageBaseViewModel.cs" />
    <Compile Include="ComponentWpf\WpfFrame.cs" />
    <Compile Include="ComponentWpf\WpfPageConfig.cs" />
    <Compile Include="Controls\AddTimes.xaml.cs">
      <DependentUpon>AddTimes.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\AllRoomView.xaml.cs">
      <DependentUpon>AllRoomView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\BookDemand.xaml.cs">
      <DependentUpon>BookDemand.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\ControlsConent\UAutoPanel.xaml.cs">
      <DependentUpon>UAutoPanel.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\ControlsConent\UAutoTypePanel.xaml.cs">
      <DependentUpon>UAutoTypePanel.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\ControlsConent\UShopConent.xaml.cs">
      <DependentUpon>UShopConent.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\DateTimePicker.xaml.cs">
      <DependentUpon>DateTimePicker.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\DepositManagement .xaml.cs">
      <DependentUpon>DepositManagement .xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\DispatcherHelper.cs" />
    <Compile Include="Controls\FileView.xaml.cs">
      <DependentUpon>FileView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\HdTextBox.xaml.cs">
      <DependentUpon>HdTextBox.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\HistoryDataReport.xaml.cs">
      <DependentUpon>HistoryDataReport.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\Inherit\TbCustTel.cs" />
    <Compile Include="Controls\LoadingWait.xaml.cs">
      <DependentUpon>LoadingWait.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\MyAdorner.cs" />
    <Compile Include="Controls\MyCustInfoShow.cs" />
    <Compile Include="Controls\NumbiricTextBox.cs" />
    <Compile Include="Controls\OpenInfoByUserId.xaml.cs">
      <DependentUpon>OpenInfoByUserId.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\OpenRoomInfo.xaml.cs">
      <DependentUpon>OpenRoomInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\OrderRoomInfo.xaml.cs">
      <DependentUpon>OrderRoomInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\ReFillOpenData.xaml.cs">
      <DependentUpon>ReFillOpenData.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\RoomsReport.xaml.cs">
      <DependentUpon>RoomsReport.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\RoomChange.xaml.cs">
      <DependentUpon>RoomChange.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UBookCount.xaml.cs">
      <DependentUpon>UBookCount.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UBookMessage.xaml.cs">
      <DependentUpon>UBookMessage.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UBookSearch.xaml.cs">
      <DependentUpon>UBookSearch.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UBusinessReport.xaml.cs">
      <DependentUpon>UBusinessReport.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UConType.xaml.cs">
      <DependentUpon>UConType.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UCustBehaInfo.xaml.cs">
      <DependentUpon>UCustBehaInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UCustInfo.xaml.cs">
      <DependentUpon>UCustInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UCust_Tel.xaml.cs">
      <DependentUpon>UCust_Tel.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UMemberInfo.xaml.cs">
      <DependentUpon>UMemberInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UMoreFunction.xaml.cs">
      <DependentUpon>UMoreFunction.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UpdOpenCacheInfo.xaml.cs">
      <DependentUpon>UpdOpenCacheInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\URadioButton.cs" />
    <Compile Include="Controls\URoom.xaml.cs">
      <DependentUpon>URoom.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\URoomInfo.xaml.cs">
      <DependentUpon>URoomInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\URtInfo.xaml.cs">
      <DependentUpon>URtInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UserCanvasShow.xaml.cs">
      <DependentUpon>UserCanvasShow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UserControlCanvas.xaml.cs">
      <DependentUpon>UserControlCanvas.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UserControlTest.xaml.cs">
      <DependentUpon>UserControlTest.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UserShowView.xaml.cs">
      <DependentUpon>UserShowView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UShop.xaml.cs">
      <DependentUpon>UShop.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\USMS.xaml.cs">
      <DependentUpon>USMS.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\USMSInfo.xaml.cs">
      <DependentUpon>USMSInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\USMSItem.xaml.cs">
      <DependentUpon>USMSItem.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UTextBox.xaml.cs">
      <DependentUpon>UTextBox.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UTime.xaml.cs">
      <DependentUpon>UTime.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UTimeInfo.xaml.cs">
      <DependentUpon>UTimeInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UTitleInfo.xaml.cs">
      <DependentUpon>UTitleInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UWeChatSearch.xaml.cs">
      <DependentUpon>UWeChatSearch.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\UWinsock.xaml.cs">
      <DependentUpon>UWinsock.xaml</DependentUpon>
    </Compile>
    <Compile Include="Converter\BoolToStringConverter.cs" />
    <Compile Include="Converter\DiscConverter.cs" />
    <Compile Include="FreezeCoupons.xaml.cs">
      <DependentUpon>FreezeCoupons.xaml</DependentUpon>
    </Compile>
    <Compile Include="lib\CefCallback.cs" />
    <Compile Include="lib\Factory\BuildFactory.cs" />
    <Compile Include="lib\Factory\WindowBuildFactory.cs" />
    <Compile Include="lib\SerivceHold.cs" />
    <Compile Include="lib\Single\SingleRunConst.cs" />
    <Compile Include="Loading\LoadingWait.xaml.cs">
      <DependentUpon>LoadingWait.xaml</DependentUpon>
    </Compile>
    <Compile Include="Msg.xaml.cs">
      <DependentUpon>Msg.xaml</DependentUpon>
    </Compile>
    <Compile Include="Page1.xaml.cs">
      <DependentUpon>Page1.xaml</DependentUpon>
    </Compile>
    <Compile Include="PageWindow.xaml.cs">
      <DependentUpon>PageWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="PreservedWine\PreservedDataine.xaml.cs">
      <DependentUpon>PreservedDataine.xaml</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="ProgramInfo\Controls\BillModelConvert.cs" />
    <Compile Include="ProgramInfo\Controls\BoolConvert.cs" />
    <Compile Include="ProgramInfo\Controls\ConTypeInfo.xaml.cs">
      <DependentUpon>ConTypeInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProgramInfo\Controls\ConTypeTimeInfo.xaml.cs">
      <DependentUpon>ConTypeTimeInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProgramInfo\Controls\CtModelConvert.cs" />
    <Compile Include="ProgramInfo\Controls\DayTypeConvert.cs" />
    <Compile Include="ProgramInfo\Controls\DisPlayBoolConvert.cs" />
    <Compile Include="ProgramInfo\Controls\IntConvert.cs" />
    <Compile Include="ProgramInfo\Controls\Isvalid.cs" />
    <Compile Include="ProgramInfo\Controls\PriceModel.xaml.cs">
      <DependentUpon>PriceModel.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProgramInfo\Controls\PrintAttributeConfig.xaml.cs">
      <DependentUpon>PrintAttributeConfig.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProgramInfo\Controls\RmInfo.xaml.cs">
      <DependentUpon>RmInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProgramInfo\Controls\RtInfo.xaml.cs">
      <DependentUpon>RtInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProgramInfo\Controls\RtPriceInfo.xaml.cs">
      <DependentUpon>RtPriceInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProgramInfo\Controls\ShopAreaInfo.xaml.cs">
      <DependentUpon>ShopAreaInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProgramInfo\Controls\ShopTimeInfo.xaml.cs">
      <DependentUpon>ShopTimeInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProgramInfo\Controls\TimeModelConvert.cs" />
    <Compile Include="ProgramInfo\Controls\UBackstage.xaml.cs">
      <DependentUpon>UBackstage.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProgramInfo\Controls\WorkBookOutInfo.xaml.cs">
      <DependentUpon>WorkBookOutInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProgramInfo\Controls\WorkConfig.xaml.cs">
      <DependentUpon>WorkConfig.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProgramInfo\Controls\WorkNotShoptime.xaml.cs">
      <DependentUpon>WorkNotShoptime.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="RegionControl.xaml.cs">
      <DependentUpon>RegionControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="ReplaceBooking\AllRoomData.xaml.cs">
      <DependentUpon>AllRoomData.xaml</DependentUpon>
    </Compile>
    <Compile Include="ReplaceBooking\LoadingWait.xaml.cs" />
    <Compile Include="ReplaceBooking\WHouseWatching.xaml.cs" />
    <Compile Include="ReplaceBooking\WInvNoDetails.xaml.cs" />
    <Compile Include="lib\Single\SingleRun.cs" />
    <Compile Include="lib\Single\SingleRunBack.cs" />
    <Compile Include="StartWindow.xaml.cs">
      <DependentUpon>StartWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="test.xaml.cs">
      <DependentUpon>test.xaml</DependentUpon>
    </Compile>
    <Compile Include="ToolLibrary\LibraryWindow.xaml.cs">
      <DependentUpon>LibraryWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="ToolLibrary\UOpenFunction.xaml.cs">
      <DependentUpon>UOpenFunction.xaml</DependentUpon>
    </Compile>
    <Compile Include="ToolLibrary\WebPage.xaml.cs">
      <DependentUpon>WebPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="ToolLibrary\WindowFun.cs" />
    <Compile Include="UChart\EventHandling.cs" />
    <Compile Include="UChart\UChartConent.cs" />
    <Compile Include="UNetworkOff.xaml.cs">
      <DependentUpon>UNetworkOff.xaml</DependentUpon>
    </Compile>
    <Compile Include="Utils\Phone_usb.cs" />
    <Compile Include="Utils\RmLock\RmLock_utils.cs" />
    <Compile Include="Utils\WindowUtils.cs" />
    <Compile Include="VerCodeShowBox.xaml.cs">
      <DependentUpon>VerCodeShowBox.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModel\ApiViewModel\ApiCallViewModel.cs" />
    <Compile Include="ViewModel\ApiViewModel\ApiViewModelBase.cs" />
    <Compile Include="ViewModel\BookPageViewModel.cs" />
    <Compile Include="ViewModel\MainViewModel.cs" />
    <Compile Include="ViewModel\OpenPageViewModel.cs" />
    <Compile Include="WActivation.xaml.cs">
      <DependentUpon>WActivation.xaml</DependentUpon>
    </Compile>
    <Compile Include="WAndUserInfo.xaml.cs">
      <DependentUpon>WAndUserInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="WBook.xaml.cs">
      <DependentUpon>WBook.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="WBox.xaml.cs">
      <DependentUpon>WBox.xaml</DependentUpon>
    </Compile>
    <Compile Include="WBrowser.xaml.cs">
      <DependentUpon>WBrowser.xaml</DependentUpon>
    </Compile>
    <Compile Include="WCall.xaml.cs">
      <DependentUpon>WCall.xaml</DependentUpon>
    </Compile>
    <Compile Include="WDateTime.xaml.cs">
      <DependentUpon>WDateTime.xaml</DependentUpon>
    </Compile>
    <Compile Include="WDeposit.xaml.cs">
      <DependentUpon>WDeposit.xaml</DependentUpon>
    </Compile>
    <Compile Include="Web References\RMSService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="WebsiteWindow.xaml.cs">
      <DependentUpon>WebsiteWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="WHistoryDataReport.xaml.cs">
      <DependentUpon>WHistoryDataReport.xaml</DependentUpon>
    </Compile>
    <Compile Include="WindowAbility.xaml.cs">
      <DependentUpon>WindowAbility.xaml</DependentUpon>
    </Compile>
    <Compile Include="Window_Pwd.xaml.cs">
      <DependentUpon>Window_Pwd.xaml</DependentUpon>
    </Compile>
    <Compile Include="WLogin.xaml.cs">
      <DependentUpon>WLogin.xaml</DependentUpon>
    </Compile>
    <Compile Include="WModifyPassword.xaml.cs">
      <DependentUpon>WModifyPassword.xaml</DependentUpon>
    </Compile>
    <Compile Include="WOpen.xaml.cs">
      <DependentUpon>WOpen.xaml</DependentUpon>
    </Compile>
    <Compile Include="WPasswordSet.xaml.cs">
      <DependentUpon>WPasswordSet.xaml</DependentUpon>
    </Compile>
    <Compile Include="WPDeposit_UnLine.xaml.cs">
      <DependentUpon>WPDeposit_UnLine.xaml</DependentUpon>
    </Compile>
    <Compile Include="WPDF.xaml.cs">
      <DependentUpon>WPDF.xaml</DependentUpon>
    </Compile>
    <Compile Include="WPdfView.xaml.cs">
      <DependentUpon>WPdfView.xaml</DependentUpon>
    </Compile>
    <Compile Include="WOpen_WechatInfo.xaml.cs">
      <DependentUpon>WOpen_WechatInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="WPreorder.xaml.cs">
      <DependentUpon>WPreorder.xaml</DependentUpon>
    </Compile>
    <Compile Include="WRommManage.xaml.cs">
      <DependentUpon>WRommManage.xaml</DependentUpon>
    </Compile>
    <Compile Include="WRoomAll.xaml.cs">
      <DependentUpon>WRoomAll.xaml</DependentUpon>
    </Compile>
    <Compile Include="WRoomManage.xaml.cs">
      <DependentUpon>WRoomManage.xaml</DependentUpon>
    </Compile>
    <Compile Include="WRoomManage_AddTime.xaml.cs">
      <DependentUpon>WRoomManage_AddTime.xaml</DependentUpon>
    </Compile>
    <Compile Include="WRoomManage_New.xaml.cs">
      <DependentUpon>WRoomManage_New.xaml</DependentUpon>
    </Compile>
    <Compile Include="WRoomsReport.xaml.cs">
      <DependentUpon>WRoomsReport.xaml</DependentUpon>
    </Compile>
    <Compile Include="WSearchList.xaml.cs">
      <DependentUpon>WSearchList.xaml</DependentUpon>
    </Compile>
    <Compile Include="WRms2009Book.xaml.cs">
      <DependentUpon>WRms2009Book.xaml</DependentUpon>
    </Compile>
    <Compile Include="WSeatCount.xaml.cs">
      <DependentUpon>WSeatCount.xaml</DependentUpon>
    </Compile>
    <Compile Include="WSMS.xaml.cs">
      <DependentUpon>WSMS.xaml</DependentUpon>
    </Compile>
    <Compile Include="WUserInfo.xaml.cs">
      <DependentUpon>WUserInfo.xaml</DependentUpon>
    </Compile>
    <Compile Include="WWeChatUser_add.xaml.cs">
      <DependentUpon>WWeChatUser_add.xaml</DependentUpon>
    </Compile>
    <Compile Include="WWeChatUser_delete.xaml.cs">
      <DependentUpon>WWeChatUser_delete.xaml</DependentUpon>
    </Compile>
    <Page Include="AccessBar.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="AppBook.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="birthday_giftPay\birthdaygiftPayManageWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ComponentWpf\View\BookPage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ComponentWpf\View\HomePage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ComponentWpf\View\LoginPage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ComponentWpf\View\MainPage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ComponentWpf\View\OpenPage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\AddTimes.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\AllRoomView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\BookDemand.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\ControlsConent\UAutoPanel.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\ControlsConent\UAutoTypePanel.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\ControlsConent\UShopConent.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\DateTimePicker.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\DepositManagement .xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\FileView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\HdTextBox.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\HistoryDataReport.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\LoadingWait.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\OpenInfoByUserId.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\OpenRoomInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\OrderRoomInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\ReFillOpenData.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\RoomsReport.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\RoomChange.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UBookCount.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UBookMessage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UBookSearch.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UBusinessReport.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UConType.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UCustBehaInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UCustInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UCust_Tel.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UMemberInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UMoreFunction.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UpdOpenCacheInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\URoom.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\URoomInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\URtInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UserCanvasShow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UserControlCanvas.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UserControlTest.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Controls\UserShowView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UShop.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\USMS.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\USMSInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\USMSItem.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UTextBox.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UTime.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UTimeInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UTitleInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UWeChatSearch.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\UWinsock.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="FreezeCoupons.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Loading\LoadingWait.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="Msg.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Page1.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="PageWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="PreservedWine\PreservedDataine.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ProgramInfo\Controls\ConTypeInfo.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ProgramInfo\Controls\ConTypeTimeInfo.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ProgramInfo\Controls\PriceModel.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ProgramInfo\Controls\PrintAttributeConfig.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ProgramInfo\Controls\RmInfo.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ProgramInfo\Controls\RtInfo.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ProgramInfo\Controls\RtPriceInfo.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ProgramInfo\Controls\ShopAreaInfo.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ProgramInfo\Controls\ShopTimeInfo.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ProgramInfo\Controls\UBackstage.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ProgramInfo\Controls\WorkBookOutInfo.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ProgramInfo\Controls\WorkConfig.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ProgramInfo\Controls\WorkNotShoptime.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="RegionControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ReplaceBooking\AllRoomData.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ReplaceBooking\LoadingWait.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ReplaceBooking\WHouseWatching.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ReplaceBooking\WInvNoDetails.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="StartWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Styles\Dictionary1.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="test.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ToolLibrary\LibraryWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ToolLibrary\UOpenFunction.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ToolLibrary\WebPage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UNetworkOff.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="VerCodeShowBox.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WActivation.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WAndUserInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WBook.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WBox.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WBrowser.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WCall.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WDateTime.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WDeposit.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WebsiteWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WHistoryDataReport.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WindowAbility.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Window_Pwd.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WLogin.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WModifyPassword.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="WOpen.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="WPasswordSet.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WPDeposit_UnLine.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WPDF.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WPdfView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WOpen_WechatInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WPreorder.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WRommManage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WRoomAll.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WRoomManage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WRoomManage_AddTime.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WRoomManage_New.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WRoomsReport.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WSearchList.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WRms2009Book.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WSeatCount.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WSMS.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WUserInfo.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WWeChatUser_add.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WWeChatUser_delete.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="App1.config" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <AppDesigner Include="Properties\" />
    <None Include="Web References\RMSService\MArea_MShop.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\RMSService\MBillStatus.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\RMSService\MShopInfo.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\RMSService\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\RMSService\RMSBasicInfo.wsdl" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RMSBLLApi\RMSBLLApi.csproj">
      <Project>{a9248443-13ac-494f-893e-5a14bf8aa981}</Project>
      <Name>RMSBLLApi</Name>
    </ProjectReference>
    <ProjectReference Include="..\RMSBLL\RMSBLL.csproj">
      <Project>{0739592f-022c-4134-8566-38fea067c196}</Project>
      <Name>RMSBLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\RMSModel\RMSModel.csproj">
      <Project>{06f53fbd-cad4-46cf-b715-c2e800f2c05d}</Project>
      <Name>RMSModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\RMSUtils\RMSUtils.csproj">
      <Project>{f35883d8-8f4b-4749-b0fc-fde1384d15e6}</Project>
      <Name>RMSUtils</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Common\" />
    <Folder Include="Global\" />
    <Folder Include="Themes\" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 %28x86 �� x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.4.5">
      <Visible>False</Visible>
      <ProductName>Windows Installer 4.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\540032316341445923.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\809144169268797654.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\img_3.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\img_1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\img_2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\img_4.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\stop.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\horn.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resources\textbox.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\discount.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\vip.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\_809144169268797654.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\available.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\tot.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\_809144169268797654.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Edition.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="bitbug_favicon.ico" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\CHN.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\HKG.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\More.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\print_off.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\print_on.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ok.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\user.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Own.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Refresh.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\clear.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\money.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="bin\Debug\Resources\purchase.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\DataQuery.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\DataReport.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Reset.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\WechatPay.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Wechat.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\RoomManage.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\RoomTime.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Deposit.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Behavior.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Message.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Birthday.png" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <WebReferences Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <WebReferenceUrl Include="http://rms.tang-hui.com.cn/WebService/RMSBasicInfo.asmx">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\RMSService\</RelPath>
      <UpdateFromURL>http://rms.tang-hui.com.cn/WebService/RMSBasicInfo.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>RMS2018_RMSService_RMSBasicInfo</CachedSettingsPropName>
    </WebReferenceUrl>
  </ItemGroup>
  <ItemGroup>
    <None Include="Web References\RMSService\RMSBasicInfo.disco" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>