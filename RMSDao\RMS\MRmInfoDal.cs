
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RMSModel.RMS;
using RMSModel.ExtensionRMS;
using RMSModel.DbFood;
using RMSModel;

namespace RMSDao.RMS
{
    public static class MRmInfoDal
    {
        /// <summary>
        /// 查询MRmInfoDal房间信息表的所有信息
        /// </summary>
        /// <returns></returns>
        public static List<MRm_Rt_MArea_MShop> GetMRmInfo()
        {
            //            string sql = @"select * from RmInfo rm inner join AreaInfo ar on rm.AreaId =ar.AreaId 
            //            inner join RtInfo rt on  rm.RtNo=rt.RtNo  inner join ShopInfo si on rm.ShopId=si.ShopId and ar.ShopId=si.ShopId and rt.ShopId=si.ShopId Order By rm.ShopId";
            //2021-04-19 jjy 修改,房间是或否展示错误
            //20250702 lsf,加上ar.Sn
            string sql = @"select  rm.*,ar.<PERSON>,ar.<PERSON>,ar.<PERSON>,ar.Sn,rt.IRtKey,rt.IsWechatBook,rt.NumberMax,rt.NumberMin,rt.RtName,rt.RtNo,si.* from RmInfo rm inner join AreaInfo ar on rm.AreaId =ar.AreaId 
            inner join RtInfo rt on  rm.RtNo=rt.RtNo  inner join ShopInfo si on rm.ShopId=si.ShopId and ar.ShopId=si.ShopId and rt.ShopId=si.ShopId Order By rm.ShopId";
            return DbHelp_rms2019<MRm_Rt_MArea_MShop>.dao.sqlDataToList(sql);
        }


        /// <summary>
        /// 查询MRmInfoDal房间信息表的所有信息
        /// </summary>
        /// <returns></returns>
        public static List<RMSModel.RMS.Api.MRmInfoApi> GetMRmInfo_Api()
        {
            string sql = @"select ShopId,RtNo from RmInfo";
            return DbHelp_rms2019<RMSModel.RMS.Api.MRmInfoApi>.dao.sqlDataToList(sql);
        }

        /// <summary>
        /// 给MRmInfoDal房间信息表添加一条信息
        /// </summary>
        /// <returns></returns>
        public static int GetMRmInfo_Add(MRmInfo RmInfo)
        {
            RmInfo.RmsStatus = "E";
            string sql = @"insert into RmInfo(ShopId,RmNo,RtNo,AreaId,IsExistWc,MealAreaSort,FloorNumber,IsDisplay,RmsStatus) values
            (@ShopId,@RmNo,@RtNo,@AreaId,@IsExistWc,@MealAreaSort,@FloorNumber,@IsDisplay,@RmsStatus)";
            return DbHelp_rms2019<int>.dao.OperationData(sql, RmInfo);
        }

        /// <summary>
        /// 给MRmInfoDal房间信息表删除一条信息
        /// </summary>
        /// <returns></returns>
        public static int GetMRmInfo_Del(MRmInfo RmInfo)
        {
            string sql = "delete from RmInfo where RmNo=@RmNo and ShopId=@ShopId";
            return DbHelp_rms2019<int>.dao.OperationData(sql, RmInfo);
        }

        /// <summary>
        /// 给MRmInfoDal房间信息表批量删除多条信息
        /// </summary>
        /// <returns></returns>
        public static int GetMRmInfo_Del(string RmNo, string shopid)
        {
            string sql = "delete from RmInfo where RmNo = @RmNo  and ShopId=@ShopId ";
            DbHelp_rms2019<int>.dao.OperationData(sql, new { RmNo = RmNo, ShopId = shopid });
            return 1;
        }

        /// <summary>
        /// 给MRmInfoDal房间信息表修改一条信息
        /// </summary>
        /// <returns></returns>
        public static int GetMRmInfo_Udp(string RtUp, string oldRmNo, string oldshopid)
        {
            string sql = string.Format(@"update RmInfo set RtUp=@RtUp where RmNo ='{0}' and ShopId={1}", oldRmNo, oldshopid);
            return DbHelp_rms2019<int>.dao.OperationData(sql, new { RtUp = RtUp });
        }

        /// <summary>
        /// 给MRmInfoDal房间信息表修改一条信息
        /// </summary>
        /// <returns></returns>
        public static int GetMRmInfo_Udp(MRmInfo RmInfo, string oldRmNo, string oldshopid)
        {
            string sql = string.Format(@"update RmInfo set ShopId=@ShopId , RmNo=@RmNo ,RtNo=@RtNo,AreaId=@AreaId,
            IsExistWc=@IsExistWc,MealAreaSort=@MealAreaSort,FloorNumber=@FloorNumber,IsDisplay=@IsDisplay where RmNo ='{0}' and ShopId={1}", oldRmNo, oldshopid);
            return DbHelp_rms2019<int>.dao.OperationData(sql, RmInfo);
        }


        public static int SetMRmInfo_Remark(int shopid, string RmNo, string remark)
        {
            string sql = string.Format(@"update RmInfo set remark=@remark where RmNo=@RmNo and  ShopId=@ShopId");
            return DbHelp_rms2019<int>.dao.OperationData(sql, new { RmNo = RmNo, ShopId = shopid, remark });
        }
        public static string GetMRmInfo_Remark(int shopid, string RmNo)
        {
            string sql = string.Format(@"select remark from  RmInfo  where RmNo=@RmNo and  ShopId=@ShopId");
            return DbHelp_rms2019<string>.dao.sqlSingleData(sql, new { RmNo = RmNo, ShopId = shopid });
        }

        /// <summary>
        /// 给MRmInfoDal房间信息表修改一条信息
        /// </summary>
        /// <param name="shopid">门店id</param>
        /// <param name="RmNo">房号</param>
        /// <param name="RmsStatus">状态</param>
        /// <param name="InvNo">账单号</param>
        /// <param name="RtUp">当房</param>
        /// <param name="RoomToIkey">派房key</param>
        /// <returns></returns>
        public static int GetMRmInfo_Udp(int shopid, string RmNo, string RmsStatus, string InvNo, string RtUp, string RoomToIkey, string BookNo, string CustTel, bool IsDiscount = false, string remark = "")
        {
            string sql = string.Format(@"update RmInfo set  RmsStatus=@RmsStatus,InvNo=@InvNo,RtUp=@RtUp,RoomToIkey=@RoomToIkey ,BookNo=@BookNo,CustTel=@CustTel,IsDiscount=@IsDiscount,remark=@remark where RmNo=@RmNo and  ShopId=@ShopId");
            return DbHelp_rms2019<int>.dao.OperationData(sql, new { RmNo = RmNo, ShopId = shopid, RmsStatus = RmsStatus, InvNo = InvNo, RtUp = RtUp, RoomToIkey = RoomToIkey, BookNo = BookNo, CustTel = CustTel, IsDiscount = IsDiscount, remark });
        }
        /// <summary>
        /// 修改折扣信息
        /// </summary>
        /// <param name="shopid">门店id</param>
        /// <param name="RmNo">房号</param>
        /// <param name="IsDiscount">折扣</param>
        /// <returns></returns>
        public static int GetMRmInfo_Udp(int shopid, string RmNo, int IsDiscount)
        {
            string sql = string.Format(@"update RmInfo set  IsDiscount=@IsDiscount where RmNo=@RmNo and  ShopId=@ShopId");
            return DbHelp_rms2019<int>.dao.OperationData(sql, new { RmNo = RmNo, ShopId = shopid, IsDiscount = IsDiscount });
        }

        /// <summary>
        /// 修改状态信息
        /// </summary>
        /// <param name="shopid">门店id</param>
        /// <param name="RmNo">房号</param>
        /// <param name="IsDiscount">折扣</param>
        /// <returns></returns>
        public static int GetMRmInfo_Udp(int shopid, string RmNo, string RmsStatus)
        {
            string sql = string.Format(@"update RmInfo set  RmsStatus=@RmsStatus where RmNo=@RmNo and  ShopId=@ShopId");
            return DbHelp_rms2019<int>.dao.OperationData(sql, new { RmNo = RmNo, ShopId = shopid, RmsStatus = RmsStatus });
        }

        /// <summary>
        /// 给MRmInfoDal房间信息表修改一条信息(转房)
        /// </summary>
        /// <param name="RtUp">当房</param>
        /// <param name="RoomToIkey">派房key</param>
        /// <param name="BillTot">账单总金额</param>
        /// <param name="BookNo"> 预约号</param>
        /// <param name="CustTel">顾客电话</param>
        /// <param name="IsDiscount">是否折扣   0否： 1折扣</param>
        /// <param name="RmsStatus">状态</param>
        /// <returns></returns>
        public static int GetMRmInfo_Udp(int shopid, string RmNo, string RtUp, string RoomToIkey, int BillTot, string BookNo, string CustTel, int IsDiscount, string RmsStatus)
        {
            string sql = string.Format(@"update RmInfo set RtUp=@RtUp,RoomToIkey=@RoomToIkey,BillTot=@BillTot,BookNo=@BookNo,CustTel=@CustTel,IsDiscount=@IsDiscount,RmsStatus=@RmsStatus where RmNo=@RmNo and  ShopId=@ShopId");
            return DbHelp_rms2019<int>.dao.OperationData(sql, new { RmNo = RmNo, ShopId = shopid, RtUp = RtUp, RoomToIkey = RoomToIkey, BillTot = BillTot, BookNo = BookNo, CustTel = CustTel, IsDiscount = IsDiscount, RmsStatus = RmsStatus });
        }

        /// <summary>
        /// 修改房间信息
        /// </summary>
        /// <param name="shopid">门店</param>
        /// <param name="RoomToNo">转入房间</param>
        /// <param name="RoomFromNo">转出房间</param>
        /// <returns></returns>
        public static int GetMRmInfo_Up(int shopid, string RoomToNo, string RoomFromNo, string UserName)
        {
            //string sql = string.Format(@"update RmInfo set RtUp=@RtUp,RoomToIkey=@RoomToIkey,BillTot=@BillTot,BookNo=@BookNo,CustTel=@CustTel,IsDiscount=@IsDiscount,RmsStatus=@RmsStatus where RmNo=@RmNo and  ShopId=@ShopId");
            string sql = "GetMRmInfo_Udp";
            return DbHelp_rms2019<int>.dao.pro(sql, new { ShopId = shopid, RoomToNo = RoomToNo, RoomFromNo = RoomFromNo, UserName = UserName });
        }
        /// <summary>
        /// 更新天王状态到rms2019
        /// </summary>
        /// <param name="shopid">门店id</param>
        /// <param name="RmNo">房号</param>
        /// <param name="RmsStatus">状态</param>
        /// <param name="InvNo">账单号</param>
        /// <param name="DbfoodStatus">天王状态</param>
        /// <returns></returns>
        public static int SetRoomToDbFood(int shopid, string RmNo, string RmsStatus, string InvNo, string DbfoodStatus, int billTot, int IsDiscount)
        {
            string sql = string.Format(@"update RmInfo set  RmsStatus=@RmsStatus,DbfoodStatus=@DbfoodStatus,InvNo=@InvNo,billTot=@billTot,IsDiscount=@IsDiscount where RmNo=@RmNo and  ShopId=@ShopId ");
            return DbHelp_rms2019<int>.dao.OperationData(sql, new { RmNo = RmNo, ShopId = shopid, RmsStatus = RmsStatus, InvNo = InvNo, DbfoodStatus = DbfoodStatus, IsDiscount = IsDiscount, billTot = billTot });
        }
        /// <summary>
        /// 更新天王状态到rms2019
        /// </summary>
        /// <param name="shopid">门店id</param>
        /// <param name="RmNo">房号</param>
        /// <param name="RmsStatus">状态</param>
        /// <param name="InvNo">账单号</param>
        /// <param name="DbfoodStatus">天王状态</param>
        /// <returns></returns>
        public static int SetRoomToDbFood(int shopid, string RmNo, string RmsStatus, string InvNo, string DbfoodStatus, int billTot, string RoomToIkey)
        {
            string sql = string.Format(@"update RmInfo set  RmsStatus=@RmsStatus,DbfoodStatus=@DbfoodStatus,InvNo=@InvNo,billTot=@billTot,RoomToIkey=@RoomToIkey where RmNo=@RmNo and  ShopId=@ShopId ");
            return DbHelp_rms2019<int>.dao.OperationData(sql, new { RmNo = RmNo, ShopId = shopid, RmsStatus = RmsStatus, InvNo = InvNo, DbfoodStatus = DbfoodStatus, RoomToIkey = RoomToIkey, billTot = billTot });
        }


        /// <summary>
        /// 更新天王状态到rms2019
        /// </summary>
        /// <param name="shopid">门店id</param>
        /// <param name="RmNo">房号</param>
        /// <param name="RmsStatus">状态</param>
        /// <param name="InvNo">账单号</param>
        /// <param name="DbfoodStatus">天王状态</param>
        /// <returns></returns>
        public static int SetRoomToDbFood_kFee(int shopid, string RmNo, string RmsStatus, string InvNo, string DbfoodStatus, int billTot, string RoomToIkey, double kFee)
        {
            string sql = string.Format(@"update RmInfo set  RmsStatus=@RmsStatus,DbfoodStatus=@DbfoodStatus,InvNo=@InvNo,billTot=@billTot,RoomToIkey=@RoomToIkey,kFee=@kFee where RmNo=@RmNo and  ShopId=@ShopId ");
            return DbHelp_rms2019<int>.dao.OperationData(sql, new { RmNo = RmNo, ShopId = shopid, RmsStatus = RmsStatus, InvNo = InvNo, DbfoodStatus = DbfoodStatus, RoomToIkey = RoomToIkey, billTot = billTot, kFee });
        }
        /// <summary>
        /// 更新天王状态到rms2019
        /// </summary>
        /// <param name="shopid">门店id</param>
        /// <param name="RmNo">房号</param>
        /// <param name="RmsStatus">状态</param>
        /// <param name="InvNo">账单号</param>
        /// <param name="DbfoodStatus">天王状态</param>
        /// <returns></returns>
        public static int SetRoomToDbFood(int shopid, string RmNo, string RmsStatus, string InvNo, string DbfoodStatus, int billTot, string CustTel, int IsDiscount, string BookNo)
        {
            string sql = string.Format(@"update RmInfo set  RmsStatus=@RmsStatus,DbfoodStatus=@DbfoodStatus,InvNo=@InvNo,billTot=@billTot,CustTel=@CustTel,IsDiscount=@IsDiscount,BookNo=@BookNo where RmNo=@RmNo and  ShopId=@ShopId ");
            return DbHelp_rms2019<int>.dao.OperationData(sql, new { RmNo = RmNo, ShopId = shopid, RmsStatus = RmsStatus, InvNo = InvNo, DbfoodStatus = DbfoodStatus, billTot = billTot, CustTel = CustTel, IsDiscount = IsDiscount, BookNo = BookNo });
        }


        /// <summary>
        /// 获取指定分店的所有房间号，根据区域进行分区展示
        /// </summary>
        /// <returns></returns>
        public static List<MRm_Rt_MArea_MShop> GetMRmInfo(int ShopId)
        {
            //            string sql = @"select * from RmInfo rm inner join AreaInfo ar on rm.AreaId =ar.AreaId 
            //            inner join RtInfo rt on  rm.RtNo=rt.RtNo  inner join ShopInfo si on rm.ShopId=si.ShopId and ar.ShopId=si.ShopId and rt.ShopId=si.ShopId where rm.ShopId=@ShopId  order by rm.RmNo asc ";
            //2021-04-19 jjy 修改,房间是或否展示错误
            //20250702 lsf,加上ar.Sn
            string sql = @"select rm.*,ar.AreaKey,ar.AreaId,ar.AreaName,ar.Sn,rt.IRtKey,rt.IsWechatBook,rt.NumberMax,rt.NumberMin,rt.RtName,rt.RtNo,si.* from RmInfo rm inner join AreaInfo ar on rm.AreaId =ar.AreaId 
            inner join RtInfo rt on  rm.RtNo=rt.RtNo  inner join ShopInfo si on rm.ShopId=si.ShopId and ar.ShopId=si.ShopId and rt.ShopId=si.ShopId where rm.ShopId=@ShopId  order by rm.RmNo asc ";
            //group by ar.AreaName,rt.rtno order by rt.rtno
            return DbHelp_rms2019<MRm_Rt_MArea_MShop>.dao.sqlDataToList(sql, new { ShopId = ShopId });
        }

        /// <summary>
        /// 查询指定门店的所有不需要同步天王状态的房间如
        /// </summary>
        /// <returns></returns>
        public static List<MRmInfoStatus> GetRmInfoNotDbFood(int ShopId)
        {
            string sql = @"select kFee,CloseTime,b.CtNo as  CtNo,b.IsBirthday, RoomToIkey,a.RmNo,a.BillTot,a.InvNo,DbFoodStatus,RmsStatus,ISNULL(b.BookNo,'') as BookNo, ISNULL(b.CustTel,'') as CustTel,ISNULL(b.CustName,'') as CustName,b.Val1 as IsDiscount,b.BookStatus,ISNULL(b.orderUserName,'') as orderUserName,ISNULL(b.OrderUserId,'') as OrderUserId from rminfo  a  left join  opencacheinfo b on a.RoomToIkey=b.Ikey where  a.shopid=@ShopId";

            return DbHelp_rms2019<MRmInfoStatus>.dao.sqlDataToList(sql, new { ShopId = ShopId });
        }
        /// <summary>
        /// 查询指定门店的所有不需要同步天王状态的房间如(API专用)
        /// </summary>
        /// <returns></returns>
        public static List<RMSModel.RMS.Api.MRmInfoStatus> GetRmInfoNotDbFoodToAip(string ShopId)
        {
            string sql = @"select AreaId as ARN,FloorNumber as FLN,a.RtNo,b.CtNo as  CtNo,b.IsBirthday, RoomToIkey,a.RmNo,a.BillTot,a.InvNo,DbFoodStatus,RmsStatus,ISNULL(b.BookNo,'') as BookNo, ISNULL(b.CustTel,'') as CustTel,ISNULL(b.CustName,'') as CustName,b.Val1 as IsDiscount,b.BookStatus,ISNULL(b.orderUserName,'') as orderUserName,ISNULL(b.OrderUserId,'') as OrderUserId from rminfo  a  left join  opencacheinfo b on a.RoomToIkey=b.Ikey  where a.shopid=@ShopId";
            return DbHelp_rms2019<RMSModel.RMS.Api.MRmInfoStatus>.dao.sqlDataToList(sql, new { ShopId = ShopId });
        }


        /// <summary>
        /// 查询指定门店的所有不需要同步天王状态的房间如(API专用)
        /// </summary>
        /// <returns></returns>
        public static List<RMSModel.RMS.Api.MRmInfoStatus_v2> GetRmInfoNotDbFoodToAip_v2(string ShopId)
        {
            string sql = @"select AreaId as ARN,FloorNumber as FLN,a.RtNo,b.CtName as  CtName,b.IsBirthday as IsBday,a.RmNo,a.BillTot,a.InvNo,DbFoodStatus,RmsStatus,

b.BookNo,  b.CustTel, b.CustName,b.Val1 as IsDiscnt,b.BookStatus,b.orderUserName,b.OrderUserId as OrderUserId,CloseTime,kFee,ComeTime,a.Remark from rminfo  a 
left join  opencacheinfo b on a.RoomToIkey=b.Ikey  where a.shopid=@ShopId";
            return DbHelp_rms2019<RMSModel.RMS.Api.MRmInfoStatus_v2>.dao.sqlDataToList(sql, new { ShopId = ShopId });
        }
        /// <summary>
        /// 查询指定门店的所有不需要同步天王状态的房间如
        /// </summary>
        /// <returns></returns>
        public static List<RMSModel.RMS.Api.MRmInfoStatus> GetRmInfoNotDbFood()
        {
            string sql = @"select a.RtNo,a.shopid,b.CtNo as  CtNo,b.IsBirthday, RoomToIkey,a.RmNo,a.BillTot,a.InvNo,DbFoodStatus,RmsStatus,ISNULL(b.BookNo,'') as BookNo, ISNULL(b.CustTel,'') as CustTel,ISNULL(b.CustName,'') as CustName,b.Val1 as IsDiscount,b.BookStatus,ISNULL(b.orderUserName,'') as orderUserName,ISNULL(b.OrderUserId,'') as OrderUserId from rminfo  a  left join  opencacheinfo b on a.RoomToIkey=b.Ikey";
            return DbHelp_rms2019<RMSModel.RMS.Api.MRmInfoStatus>.dao.sqlDataToList(sql);
        }

        /// <summary>
        /// 查询房间状态
        /// </summary>
        /// <param name="RmNo">房号</param>
        /// <param name="CustName">顾客姓名</param>
        /// <param name="Number">人数</param>
        /// <returns></returns>
        public static List<MRmInfoStatus> GetRmsRoom()
        {
            string sql = @"select  kFee,CloseTime,CtNo ,IsBirthday, RoomToIkey,RmNo,BillTot,InvNo,DbFoodStatus,RmsStatus,BookNo, CustTel,CustName,IsDiscount,BookStatus,orderUserName,OrderUserId from RmsRoom ";
            return DbHelp_dbfood<MRmInfoStatus>.dao.sqlDataToList(sql, null);
        }


        /// <summary>
        /// 查询指定门店的所有不需要同步天王状态的房间如
        /// </summary>
        /// <returns></returns>
        public static List<MRmInfoStatus> GetRmInfoNotDbFood(int ShopId, DateTime time)
        {
            string sql = @"select  kFee,CloseTime,b.CtNo as  CtNo,b.IsBirthday, RoomToIkey,a.RmNo,a.BillTot,a.InvNo,DbFoodStatus,RmsStatus,ISNULL(b.BookNo,'') as BookNo, ISNULL(b.CustTel,'') as CustTel,ISNULL(b.CustName,'') as CustName,b.Val1 as IsDiscount,b.BookStatus,ISNULL(b.orderUserName,'') as orderUserName,ISNULL(b.OrderUserId,'') as OrderUserId from rminfo  a
 join rminfoChanged c on a.Id=c.RmID
 left join  opencacheinfo b on a.RoomToIkey=b.Ikey where time>@time and  a.shopid=@ShopId";
            return DbHelp_rms2019<MRmInfoStatus>.dao.sqlDataToList(sql, new { ShopId = ShopId, time = time });
        }

        /// <summary>
        /// 根据根据房号和门店ID查询房间信息(返回对象集合)
        /// </summary>
        /// <returns></returns>
        public static List<MRm_Rt_MArea_MShop> GetMRmInfo(string RmNo, int ShopId)
        {
            string sql = @"select * from RmInfo rm inner join AreaInfo ar on rm.AreaId =ar.AreaId 
            inner join RtInfo rt on  rm.RtNo=rt.RtNo  inner join ShopInfo si on rm.ShopId=si.ShopId and ar.ShopId=si.ShopId and rt.ShopId=si.ShopId where rm.ShopId=@ShopId and rm.RmNo=@RmNo";
            return DbHelp_rms2019<MRm_Rt_MArea_MShop>.dao.sqlDataToList(sql, new { RmNo = RmNo, ShopId = ShopId });
        }


        public static MRm_Rt_MArea_MShop GetMRmInfoByInvno(string shopid, string invno)
        {
            string sql = @"select* from rminfo where  SHOPID=@shopid AND  InvNo=@invno";
            return DbHelp_rms2019<MRm_Rt_MArea_MShop>.dao.sqlSingleData(sql, new { shopid, invno });
        }


        /// <summary>
        /// 根据根据房号和门店ID查询房间信息(返回对象)
        /// </summary>
        /// <returns></returns>
        public static MRm_Rt_MArea_MShop GetMRmInfoByRmNo(string RmNo, int ShopId)
        {
            string sql = @"select * from RmInfo rm inner join AreaInfo ar on rm.AreaId =ar.AreaId 
            inner join RtInfo rt on  rm.RtNo=rt.RtNo  inner join ShopInfo si on rm.ShopId=si.ShopId and ar.ShopId=si.ShopId and rt.ShopId=si.ShopId where rm.ShopId=@ShopId and rm.RmNo=@RmNo";
            return DbHelp_rms2019<MRm_Rt_MArea_MShop>.dao.sqlSingleData(sql, new { RmNo = RmNo, ShopId = ShopId });
        }

        /// <summary>
        /// 设置关房时间
        /// </summary>
        /// <param name="ShopId"></param>
        /// <param name="RmNo"></param>
        /// <param name="CloseTime"></param>
        /// <returns></returns>
        public static int SetCloseTime(int ShopId, string RmNo, DateTime CloseTime)
        {
            string sql = @"update RmInfo set CloseTime=@CloseTime where RmNo=@RmNo and ShopId=@ShopId ";
            return DbHelp_rms2019<object>.dao.OperationData(sql, new { CloseTime = CloseTime, RmNo = RmNo, ShopId = ShopId });

        }



        /// <summary>
        /// 延迟关房
        /// </summary>
        /// <param name="ShopId"></param>
        /// <param name="RmNo"></param>
        /// <param name="delaynumber"></param>
        /// <returns></returns>
        public static int SetDelaynClose(int ShopId, string RmNo, DateTime time)
        {
            string sql = @"update  RmInfo set CloseTime=@time where RmNo=@RmNo and ShopId=@ShopId";
            return DbHelp_rms2019<object>.dao.OperationData(sql, new { RmNo = RmNo, ShopId = ShopId, time = time });

        }
        /// <summary>
        /// 延迟关房
        /// </summary>
        /// <param name="ShopId"></param>
        /// <param name="RmNo"></param>
        /// <param name="delaynumber"></param>
        /// <returns></returns>
        public static int SetDelaynClose(int ShopId, string RmNo, int delaynumber)
        {
            //string sql = @"update  RmInfo set CloseTime=date_sub(CloseTime,interval -"+ delaynumber + " Minute)where RmNo=@RmNo and ShopId=@ShopId";
            string sql = @"update  RmInfo set CloseTime=DATEADD(minute," + delaynumber + ",CloseTime) where RmNo=@RmNo and ShopId=@ShopId";
            return DbHelp_rms2019<object>.dao.OperationData(sql, new { RmNo = RmNo, ShopId = ShopId });

        }

        /// <summary>
        /// 提前关房
        /// </summary>
        /// <param name="ShopId"></param>
        /// <param name="RmNo"></param>
        /// <param name="delaynumber"></param>
        /// <returns></returns>
        public static int SetNowClose(int ShopId, string RmNo)
        {
            string sql = @"update  RmInfo set CloseTime=getdate() where RmNo=@RmNo and ShopId=@ShopId";
            return DbHelp_rms2019<object>.dao.OperationData(sql, new { RmNo = RmNo, ShopId = ShopId });

        }
        /// <summary>
        /// 设置房间状态
        /// </summary>
        /// <param name="ShopId"></param>
        /// <param name="RmNo"></param>
        /// <param name="CloseTime"></param>
        /// <returns></returns>
        public static int SetOpenStatus(int ShopId, string RmNo, string OpenStatus, string ConStatus)
        {
            string sql = @"update RmInfo set OpenStatus=@OpenStatus,ConStatus=@ConStatus where RmNo=@RmNo and ShopId=@ShopId ";
            return DbHelp_rms2019<object>.dao.OperationData(sql, new { RmNo = RmNo, ShopId = ShopId, OpenStatus = OpenStatus, ConStatus = ConStatus });

        }
        /// <summary>
        /// 获取关房时间
        /// </summary>
        /// <param name="ShopId"></param>
        /// <param name="RmNo"></param>
        /// <returns></returns>
        public static List<RmClose> GetCloseTime(int ShopId)
        {
            // string sql = @"select CloseTime,RmNo,OpenStatus,ConStatus,InvNo,RmsStatus,dbfoodStatus,RoomToIkey,kFee from  RmInfo  where ShopId=@ShopId ";
            string sql = @" select CloseTime,a.RmNo,OpenStatus,ConStatus,a.InvNo,RmsStatus,dbfoodStatus,RoomToIkey,kFee ,b.Beg_Key
from  RmInfo  as a  left join opencacheinfo b on a.RoomToIkey=b.Ikey

 where a.ShopId=@ShopId";
            return DbHelp_rms2019<RmClose>.dao.sqlDataToList(sql, new { ShopId = ShopId });

        }

        public static List<ExchangeRecord> getExchangeRecord(int ShopId, int newid)
        {
            return DbHelp_rms2019<ExchangeRecord>.dao.proReturnList("getExchangeRecord", new { ShopId, newid });
        }

        /// <summary>
        /// 数据回绑
        /// </summary>
        /// <returns></returns>
        public static List<MRm_Rt_MArea_MShop> FillMRmInfo(string RmNo, int ShopId)
        {
            string sql = "ReFillRmInfo";
            return DbHelp_rms2019<MRm_Rt_MArea_MShop>.dao.proReturnList(sql, new { RmNo = RmNo, ShopId = ShopId });
        }

    }
}
